import numpy as np
from scipy.spatial import ConvexHull, QhullError

# ----------------------------
# 核心功能：剖切与体积计算 (重构版)
# ----------------------------
def slice_convex_poly_volume(points, plane_normal, plane_d, eps=1e-12):
    """
    计算一个由点定义的凸多面体的总体积，以及被一个平面切割后的两部分体积。
    此版本全面采用 SciPy.ConvexHull 进行体积计算，确保结果的精确性和可靠性。
    """
    P = np.asarray(points, dtype=np.float64)
    if P.shape[0] < 4:
        return 0.0, 0.0, 0.0
        
    n = np.asarray(plane_normal, dtype=np.float64)
    n_norm = np.linalg.norm(n)
    if n_norm < eps: return 0.0, 0.0, 0.0 # 无效的平面法线
    n = n / n_norm
    d = float(plane_d)

    # 1) 使用 SciPy 构建凸包并直接获取可靠的总体积
    try:
        hull = ConvexHull(P, qhull_options='QJ')
    except QhullError:
        # 如果点无法构成三维体（例如，所有点共面），则体积为0
        return 0.0, 0.0, 0.0
    
    total_vol = hull.volume
    if total_vol < eps:
        return 0.0, 0.0, 0.0

    # 2) 预先判断边界情况
    # "s" 为每个点到平面的有向距离
    s = P.dot(n) + d
    
    # 情况1: 如果所有点都在平面的“下方”或“内侧” (s <= 0)
    if np.all(s < eps):
        return total_vol, total_vol, 0.0

    # 情况2: 如果所有点都在平面的“上方”或“外侧” (s > 0)
    if np.all(s > -eps):
        return total_vol, 0.0, total_vol
    
    # 3) 只有在物体确实被切割时，才执行下面的复杂逻辑
    
    # 3a. 收集构成“下方”实体的所有顶点
    # 包括：所有原始顶点中位于下方的点
    below_points = P[s <= eps].tolist()
    
    # 包括：所有新生成的、位于切割平面上的交点
    for simplex in hull.simplices:
        for i in range(len(simplex)):
            p1_idx = simplex[i]
            p2_idx = simplex[(i + 1) % len(simplex)]
            
            s1 = s[p1_idx]
            s2 = s[p2_idx]
            
            # 如果一条边被平面切割
            if (s1 > eps and s2 < -eps) or (s1 < -eps and s2 > eps):
                p1 = P[p1_idx]
                p2 = P[p2_idx]
                # 计算交点
                t = s1 / (s1 - s2)
                intersection_point = p1 + t * (p2 - p1)
                below_points.append(intersection_point)

    below_points = np.array(below_points)
    
    # 3b. 对“下方”部分的点集再次使用 ConvexHull 直接计算其体积
    if below_points.shape[0] < 4:
        below_vol = 0.0
    else:
        try:
            # 去除重复点以提高 ConvexHull 的稳定性
            unique_below_points = np.unique(below_points.round(decimals=10), axis=0)
            if unique_below_points.shape[0] < 4:
                below_vol = 0.0
            else:
                hull_below = ConvexHull(unique_below_points, qhull_options='QJ')
                below_vol = hull_below.volume
        except QhullError:
            # 如果切割后的点集无法构成三维体，体积为0
            below_vol = 0.0
            
    # 4) 计算上方体积并返回
    above_vol = max(total_vol - below_vol, 0.0)
    
    # 对最终结果进行四舍五入，以获得清晰的输出
    decimals = 8
    return np.round(total_vol, decimals), np.round(below_vol, decimals), np.round(above_vol, decimals)