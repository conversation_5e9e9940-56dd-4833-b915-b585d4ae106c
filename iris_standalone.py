
from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": False})

# 导入必要的库和模块
import omni
import carb.tokens                                      # Carbonite令牌系统
import numpy as np                                      # 数值计算库
import omni.kit.commands                               # Omniverse Kit命令系统
import omni.kit.test                                   # Omniverse Kit测试框架
from isaacsim.core.api import World                    # Isaac Sim世界管理器
from isaacsim.core.utils.prims import get_prim_at_path # 获取场景对象的工具函数
from isaacsim.core.utils.stage import create_new_stage # 创建新场景的工具函数
from isaacsim.core.utils.stage import open_stage      # 打开场景文件的工具函数
from isaacsim.core.api.world import World             # Isaac Sim世界管理器
from isaacsim.core.utils.stage import add_reference_to_stage
from isaacsim.core.api.robots import Robot
from isaacsim.core.prims import Articulation
from isaacsim.core.prims import RigidPrim

# 创建一个新的场景
environment_path = "/home/<USER>/Learn_standalone_isaac/usd_assets/ground_water.usd"
omni.usd.get_context().open_stage(environment_path)
# world里面包含了SimulationContext,进行物理仿真
if World.instance():
        World.instance().clear_instance()
world = World(stage_units_in_meters=1.0)    
world.initialize_physics()

# 3. 使用修改碰撞体积之后的机器人
robot_path = "/home/<USER>/Learn_standalone_isaac/usd_assets/iris_fov.usd"
add_reference_to_stage(usd_path=robot_path, prim_path="/World/iris")

world.scene.add_ground_plane(z_position=-1.0)

# 4. 创建机器人对象
# iris_robot = world.scene.add(Robot(prim_path="/World/iris", name="iris"))
# 变更为Articulation对象
iris_robot = world.scene.add(Articulation(prim_paths_expr="/World/iris", name="iris"))
robot_prim_path = "/World/iris"
body_names = iris_robot.body_names
body_prim_paths = [f"{robot_prim_path}/{name}" for name in body_names]
print(f"为 RigidPrimView 创建的刚体路径列表: {body_prim_paths}")

# 使用这些路径创建一个 RigidPrimView
iris_rigid_view = world.scene.add(
    RigidPrim(
        prim_paths_expr=body_prim_paths,
        name="iris_rigid_view"
    )
)

# 5. 重置世界
world.reset()

# 先运行若干步，让机器人自然衰减到较为静止的状态
for i in range(10):
    simulation_app.update()
# 开始模拟,获取到timeline，等于播放按钮
# omni.timeline.get_timeline_interface().play()

from pxr import UsdGeom,Gf
import math

parts = [
    "/World/iris/base_link/visuals",
    "/World/iris/base_link/collisions",
    "/World/iris/rotor_0/visuals",
    "/World/iris/rotor_1/visuals",
    "/World/iris/rotor_2/visuals",
    "/World/iris/rotor_3/visuals"
]



from utils_calculate.geometry_utils_volume import slice_convex_poly_volume
import isaacsim.core.utils.mesh as mesh_utils
import isaacsim.core.utils.stage as stage_utils
from isaacsim.core.prims import XFormPrim

prim_path = "/World/iris/base_link/collisions"
mesh_prim = stage_utils.get_current_stage().GetPrimAtPath(prim_path)

coord_prim_path="/World/world_point"
coord_prim = stage_utils.get_current_stage().GetPrimAtPath(coord_prim_path)

world_vertices = mesh_utils.get_mesh_vertices_relative_to(mesh_prim, coord_prim)
print(world_vertices)

total, below, above =  slice_convex_poly_volume(world_vertices, plane_normal=(0,0,1),plane_d=0)
print("water_total: ", total)
print("water_below: ", below)
print("water_above: ", above)

try:
    while simulation_app.is_running():
        world.step()
except KeyboardInterrupt:
    print("用户中断仿真")
except Exception as e:
    carb.log_error(f"仿真错误: {e}")
finally:
    simulation_app.close()
    
    