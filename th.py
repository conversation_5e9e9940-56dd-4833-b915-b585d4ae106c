#!/usr/bin/env python3
"""
立方体水下体积与力学实时计算 - Standalone模式

此脚本演示了如何以结构化的方式在Isaac Sim中实时计算一个物理立方体
浸入水中的体积、浮心，并施加浮力、阻尼力。

- 目标环境: ground_water.usd
- 目标对象: 加载自外部USD的 /World/Cube_color/Cube_Xform/Cube
- 核心计算: 实时切分立方体碰撞网格，计算水下部分的体积和浮心，并应用力。
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import numpy as np
import carb
import omni.usd
from pxr import UsdGeom
from isaacsim.core.api import World                    # Isaac Sim世界管理器
from isaacsim.core.prims import RigidPrim              # 我们现在只使用RigidPrim
from isaacsim.core.utils.stage import add_reference_to_stage
from isaacsim.core.utils.prims import get_prim_at_path # 获取场景对象的工具函数
import isaacsim.core.utils.mesh as mesh_utils
import isaacsim.core.utils.stage as stage_utils
from omni.isaac.core.utils.rotations import quat_to_euler_angles # 用于姿态恢复力矩（如果需要）

# 假设 geometry_utils_volume.py 位于与此脚本相同的目录或PYTHONPATH中
from utils_calculate.geometry_utils_volume import slice_convex_poly_volume, calculate_centroid

class CubeBuoyancySim: # 类名改为 CubeBuoyancySim
    """
    立方体浮力仿真主类
    """
    def __init__(self):
        """初始化仿真环境状态"""
        self.world = None
        self.target_cube_rigid_prim = None # 存储 RigidPrim 实例，用于施加力
        self.target_collision_usd_prim = None # 存储 USD Prim 实例，用于获取网格
        self.coord_prim = None # 世界参考点 USD Prim
        
        self.time_flag = 1
        
        self.gravity_vector = np.array([0.0, 0.0, -9.81]) # 初始化为默认重力向量，防止None
        self.gravity_magnitude = 9.81                      # 初始化为默认重力大小
        
        self.world_vertices_record = None # 记录最新顶点
        
        # **力学和水环境配置**
        self.config = {
            'water_surface_z': 0.0,          # 水面高度 (Z轴)
            'water_plane_normal': (0, 0, 1), # 水平面法线 (通常Z轴向上)

            'cube_usd_asset_path': "",       # 外部传入的立方体 USD 路径
            'cube_asset_ref_root_prim_path': "/World/MyCubeAssetRef", # 立方体 USD 在 Stage 中的引用根路径
            'cube_internal_mesh_prim_path': "Cube_Xform/Cube", # 立方体在 USD 资产内部的 Mesh Prim 路径

            'fluid_density': 1000.0, # 水的密度 kg/m^3 (海水约1025)
            'linear_drag_coefficient': 10.0, # 线性阻尼系数 (牛顿/(米/秒))
            'angular_drag_coefficient': 5.0, # 角阻尼系数

            # 可选：额外的恢复力/力矩 (通常用于模拟系泊、弹簧等，而非浮体本身自扶正)
            'enable_restoring_forces': False,
            'equilibrium_position': np.array([0.0, 0.0, 0.0]), 
            'equilibrium_orientation': np.array([1.0, 0.0, 0.0, 0.0]), 
            'pos_restore_k': 50.0, 
            'pos_restore_d': 20.0, 
            'ori_restore_k': 5.0, 
            'ori_restore_d': 2.0, 
            
            'debug_print_interval': 30, # 每隔多少帧打印一次调试信息
        }

        self.coord_prim_path = "/World/world_point" # 用于 get_mesh_vertices_relative_to 的世界参考点

        self.sim_frame_count = 0

    def apply_config(self, config_dict):
        """应用外部配置参数到仿真实例"""
        self.config.update(config_dict)
        print(f"✅ 配置已应用: {len(config_dict)} 个参数")

    def setup_scene(self, environment_usd_path: str, cube_usd_path: str):
        """设置仿真世界、加载环境和立方体资产"""
        print("🌊 开始设置场景...")

        if not os.path.exists(environment_usd_path):
            carb.log_error(f"❌ 环境USD文件不存在: {environment_usd_path}")
            return False
        
        omni.usd.get_context().open_stage(environment_usd_path)
        
        self.world = World(
            physics_dt = 1.0 / 120.0, # 物理步长
            rendering_dt = 1.0 / 120.0, # 渲染步长
            stage_units_in_meters=1.0) # 舞台单位为米 (确保与您的USD资产单位匹配)

        self.world.initialize_physics()

        # 获取并记录重力向量和大小 (Isaac Sim默认处理重力)
        gravity_info = self.world.get_physics_context().get_gravity()
        if isinstance(gravity_info, (list, tuple)): # 确保转换为 numpy 数组
            self.gravity_vector = np.array(gravity_info)
        else:
            self.gravity_vector = gravity_info # 已经是numpy array
        
        self.gravity_magnitude = np.linalg.norm(self.gravity_vector)
        print(f"✅ Isaac Sim 重力设置为: {self.gravity_vector} (大小: {self.gravity_magnitude:.2f} m/s²)")

        # 3. 引用立方体USD资产到场景中
        if not os.path.exists(cube_usd_path): # 使用 cube_usd_path
            carb.log_error(f"❌ 立方体USD资产文件不存在: {cube_usd_path}")
            return False
            
        add_reference_to_stage(
            usd_path=cube_usd_path, 
            prim_path=self.config['cube_asset_ref_root_prim_path']
        )
        print(f"✅ 立方体USD资产 '{cube_usd_path}' 已引用到 '{self.config['cube_asset_ref_root_prim_path']}'")

        # 4. 构造立方体 Mesh Prim 的完整路径 (用于获取顶点数据)
        full_mesh_prim_path = f"{self.config['cube_asset_ref_root_prim_path']}/{self.config['cube_internal_mesh_prim_path']}"
        print(f"Debug: 尝试获取的目标Mesh Prim路径: {full_mesh_prim_path}")

        # 5. 获取 RigidPrim 实例 (用于施加力/力矩)
        # RigidPrim 的 prim_paths_expr 应该指向实际附加了 RigidBodyAPI 的 Prim
        # 根据你的路径结构 /World/Cube_color/Cube_Xform/Cube，
        # 实际施加力的对象应该是 /World/MyCubeAssetRef/Cube_Xform (如果RigidBody在Cube_Xform上)
        rigid_body_prim_path_for_force = f"{self.config['cube_asset_ref_root_prim_path']}/" \
                                         f"{self.config['cube_internal_mesh_prim_path'].split('/')[0]}" # /World/MyCubeAssetRef/Cube_Xform
        
        self.target_cube_rigid_prim = self.world.scene.add(
            RigidPrim(
                prim_paths_expr=rigid_body_prim_path_for_force, 
                name="my_cube_rigid_body" 
            )
        )
        
        if not self.target_cube_rigid_prim.is_valid():
            carb.log_error(f"❌ 无法创建 RigidPrim 实例或其无效: {rigid_body_prim_path_for_force}")
            return False
        
        print(f"✅ 刚体 RigidPrim 实例已锁定: {rigid_body_prim_path_for_force}")
        
        # 获取用于计算体积的目标碰撞体Prim (通常是Mesh Prim)
        self.target_collision_usd_prim = stage_utils.get_current_stage().GetPrimAtPath(full_mesh_prim_path)
        
        if not self.target_collision_usd_prim.IsValid():
            carb.log_error(f"❌ 无法找到目标碰撞体（Mesh Prim）: {full_mesh_prim_path}")
            return False
        
        # 确保 target_collision_usd_prim 是 UsdGeom.Mesh 类型
        if not self.target_collision_usd_prim.IsA(UsdGeom.Mesh):
            carb.log_error(f"❌ 目标碰撞体 {full_mesh_prim_path} 不是 UsdGeom.Mesh 类型。")
            return False
        
        print(f"✅ 目标碰撞体（Mesh Prim）已锁定: {full_mesh_prim_path}")

        # 6. 创建参考点 (用于获取世界坐标)
        self.coord_prim  = stage_utils.get_current_stage().GetPrimAtPath(self.coord_prim_path)
        if not self.coord_prim.IsValid():
             carb.log_warning(f"⚠️ 参考坐标Prim '{self.coord_prim_path}' 不存在。顶点将相对于根路径获取。")
             self.coord_prim = stage_utils.get_current_stage().GetPrimAtPath('/')

        # 7. 重置世界，让所有对象初始化
        self.world.reset()

        # 8. 注册物理回调！
        self.world.add_physics_callback(
            "cube_physics_processor", # 回调名称，更具描述性
            callback_fn=self._physics_step_callback # 绑定到新的回调函数
        )
        print("✅ 物理回调函数 'cube_physics_processor' 已注册")
        
        # 运行几帧让物理稳定
        for _ in range(5):
            simulation_app.update()
            
        print("🎉 场景设置成功!")
        return True

    def _get_mesh_vertices_safe(self):
        # 暂时禁用备用方法，优先调试主方法
        carb.log_error(f"❌ 备用顶点获取方法 {_get_mesh_vertices_safe.__name__} 尚未实现完整。")
        return None 

    def _physics_step_callback(self, step_size):
        """
        物理回调函数。此函数会在每个物理步进后自动执行，用于计算并施加力。
        """
        self.sim_frame_count += 1

        # 初始化所有施加的力/力矩为零向量，以便在任何情况下都能打印
        buoyancy_force_vector = np.array([0.0, 0.0, 0.0])
        F_drag_linear = np.array([0.0, 0.0, 0.0])
        T_drag_angular = np.array([0.0, 0.0, 0.0])
        F_restore_pos = np.array([0.0, 0.0, 0.0])
        T_restore_ori = np.array([0.0, 0.0, 0.0])


        # 确保目标 Prim 有效
        if not self.target_collision_usd_prim or not self.target_collision_usd_prim.GetPrim().IsValid():
            carb.log_error("❌ target_collision_usd_prim 无效或已销毁。")
            self.last_volume_data = None
            return
        if not self.target_cube_rigid_prim.is_valid():
            carb.log_error("❌ target_cube_rigid_prim 无效或已销毁。")
            self.last_volume_data = None
            return

        try:
            # 获取世界坐标系下的所有顶点
            # 注意：get_mesh_vertices_relative_to 的第二个参数是相对坐标系的 Prim
            # 如果你想获取世界坐标，可以传入 Stage 的根 Prim ('/')
            world_vertices = mesh_utils.get_mesh_vertices_relative_to(
                self.target_collision_usd_prim.GetPrim(), # 传入 USD Prim 对象
                stage_utils.get_current_stage().GetPrimAtPath('/') # 相对于世界坐标系
            )
            
            if world_vertices is None or len(world_vertices) == 0:
                # carb.log_warn(f"⚠️ 未能获取到 '{self.target_collision_usd_prim.GetPath()}' 的网格顶点。")
                self.last_volume_data = None
                return

            # 1. 计算浸没体积和浮心
            total_volume, submerged_volume, exposed_volume, hull_below = slice_convex_poly_volume(
                world_vertices,
                plane_normal=self.config['water_plane_normal'],
                plane_d=self.config['water_surface_z']
            )
            
            buoyancy_cog = np.array([0.0, 0.0, 0.0]) # 默认浮心
            if hull_below is not None and submerged_volume > 1e-9: # 确保淹没部分有效且有体积
                buoyancy_cog = calculate_centroid(hull_below)
            else:
                submerged_volume = 0.0 # 强制淹没体积为0
                buoyancy_cog = np.array([np.nan, np.nan, np.nan]) # 浮心无效

            self.time_flag += 1
            self.last_volume_data = {
                'total_volume': total_volume,
                'submerged_volume': submerged_volume,
                'exposed_volume': exposed_volume,
                'time_flag': self.time_flag,
                'buoyancy_center': buoyancy_cog
            }

            # 2. **计算并应用浮力**
            if self.last_volume_data['submerged_volume'] > 1e-9: 
                # 确保 self.gravity_magnitude 不为零，避免除以零错误
                if self.gravity_magnitude > 1e-6: # 使用一个小的阈值判断是否为零
                    buoyancy_direction = -self.gravity_vector / self.gravity_magnitude 
                else:
                    # 如果重力接近零，默认浮力向上 (Z轴正方向)
                    buoyancy_direction = np.array([0.0, 0.0, 1.0]) 
                
                F_buoyancy_magnitude = self.config['fluid_density'] * \
                                       self.last_volume_data['submerged_volume'] * self.gravity_magnitude
                
                buoyancy_force_vector = F_buoyancy_magnitude * buoyancy_direction
                
                self.target_cube_rigid_prim.apply_force(
                    forces=np.array([buoyancy_force_vector]), # apply_forces 期望一个数组的力
                    world_positions=np.array([self.last_volume_data['buoyancy_center']]), # 期望一个数组的位
                    is_global=True # 施加全局力
                )
            
            # 3. **计算并应用水下阻尼力与阻尼力矩**
            linear_velocity = self.target_cube_rigid_prim.get_linear_velocity()
            angular_velocity = self.target_cube_rigid_prim.get_angular_velocity()

            if self.last_volume_data['submerged_volume'] > 1e-9: # 仅在有淹没体积时施加阻尼
                # 线性阻尼力 (与线速度方向相反)
                F_drag_linear = -self.config['linear_drag_coefficient'] * linear_velocity
                self.target_cube_rigid_prim.apply_force(forces=np.array([F_drag_linear]), is_global=True) # apply_force 期望一个力数组

                # 角阻尼力矩 (与角速度方向相反)
                T_drag_angular = -self.config['angular_drag_coefficient'] * angular_velocity
                self.target_cube_rigid_prim.apply_torque(torques=np.array([T_drag_angular]), is_global=True) # apply_torque 期望一个力矩数组
            
            # 4. **可选：计算并应用恢复力与恢复力矩**
            if self.config['enable_restoring_forces']:
                current_position, current_orientation = self.target_cube_rigid_prim.get_world_pose()

                # 位置恢复力
                pos_error = current_position - self.config['equilibrium_position']
                F_restore_pos = -self.config['pos_restore_k'] * pos_error \
                                -self.config['pos_restore_d'] * linear_velocity
                self.target_cube_rigid_prim.apply_force(forces=np.array([F_restore_pos]), is_global=True)

                # 姿态恢复力矩 (简化版：假设只关心Z轴旋转)
                current_euler = quat_to_euler_angles(current_orientation)
                equilibrium_euler = quat_to_euler_angles(self.config['equilibrium_orientation'])
                yaw_error = current_euler[2] - equilibrium_euler[2] 
                yaw_error = np.arctan2(np.sin(yaw_error), np.cos(yaw_error)) 
                angular_vel_z = angular_velocity[2]
                T_restore_yaw_mag = -self.config['ori_restore_k'] * yaw_error \
                                    -self.config['ori_restore_d'] * angular_vel_z
                T_restore_ori = np.array([0.0, 0.0, T_restore_yaw_mag]) 
                self.target_cube_rigid_prim.apply_torque(torques=np.array([T_restore_ori]), is_global=True)

            # 5. 调试打印 (控制频率)
            if self.sim_frame_count % self.config['debug_print_interval'] == 0:
                cube_position, _ = self.target_cube_rigid_prim.get_world_pose()
                
                # 获取世界坐标系的物体重心
                local_cog = self.target_cube_rigid_prim.get_local_mass_and_inertia()[0] 
                cog_world = (cube_position + 
                             self.target_cube_rigid_prim.get_world_pose()[1].as_quat_array().rotate(local_cog))

                print(f"\n--- [帧 {self.sim_frame_count}] ---")
                print(f"💧 水下体积计算结果:")
                print(f"   - 总 体 积: {self.last_volume_data['total_volume']:.6f} m³")
                print(f"   - 水下体积: {self.last_volume_data['submerged_volume']:.6f} m³")
                print(f"   - 浮心 (CoB): {self.last_volume_data['buoyancy_center']}")
                print(f"   - 重心 (CoG): {cog_world}")
                print(f"   - 立方体世界位置: {cube_position}")
                print(f"   - 线速度: {linear_velocity}")
                print(f"   - 角速度: {angular_velocity}")
                print(f"   - 应用浮力: {buoyancy_force_vector}")
                print(f"   - 应用线性阻尼: {F_drag_linear}")
                print(f"   - 应用角阻尼: {T_drag_angular}")
                if self.config['enable_restoring_forces']:
                    print(f"   - 应用位置恢复力: {F_restore_pos}")
                    print(f"   - 应用姿态恢复力矩: {T_restore_ori}")


        except Exception as e:
            carb.log_error(f"❌ 物理步进回调失败: {e}")
            self.last_volume_data = None

    def step(self):
        """执行单步仿真逻辑"""
        self.world.step(render=True)


def main():
    """主函数 - 统一参数配置和仿真流程控制"""

    # ========================================
    # 🎛️ 统一参数配置区域
    # ========================================
    
    # 📁 文件路径配置
    ENV_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/ground_water.usd"
    # 假设您的立方体USD路径，其中包含 /Cube_Xform/Cube Mesh，且 Cube_Xform 是 RigidBody
    CUBE_USD_ASSET_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/Cube_color.usd" 
    # 立方体 USD 在 Stage 中被引用的根路径
    CUBE_ASSET_REF_ROOT_PATH = "/World/Cube_color_Instance" # 示例：如果不想直接覆盖 /World/Cube_color，可以给它一个实例名
    # 立方体在 USD 资产内部的 Mesh Prim 路径
    CUBE_INTERNAL_MESH_PRIM_PATH = "Cube_Xform/Cube" 
    
    # 🌊 水环境参数
    WATER_SURFACE_Z = 0.0  # 水面高度为 Z=0.0 米 (与 ground_water.usd 匹配)

    # ========================================
    # 🚀 仿真初始化和运行
    # ========================================

    sim = CubeBuoyancySim() # 实例化 CubeBuoyancySim

    config_to_apply = {
        'water_surface_z': WATER_SURFACE_Z,
        'cube_usd_asset_path': CUBE_USD_ASSET_PATH,
        'cube_asset_ref_root_prim_path': CUBE_ASSET_REF_ROOT_PATH,
        'cube_internal_mesh_prim_path': CUBE_INTERNAL_MESH_PRIM_PATH, # 注意这里是 Mesh Prim 路径
        # 'fluid_density': 1000.0, # 如果要修改默认值，在这里设置
        # 'linear_drag_coefficient': 10.0, # 如果要修改默认值，在这里设置
        # 'angular_drag_coefficient': 5.0, # 如果要修改默认值，在这里设置
        'enable_restoring_forces': False, # 默认不启用，除非你需要额外的弹簧效果
        'debug_print_interval': 30, # 每 30 帧打印一次
        # 'equilibrium_position': np.array([0.0, 0.0, 0.0]), # 如果启用恢复力，需要设置
    }
    sim.apply_config(config_to_apply)

    # 设置场景
    if not sim.setup_scene(environment_usd_path=ENV_USD_PATH, cube_usd_path=CUBE_USD_ASSET_PATH): # 传入 cube_usd_path
        carb.log_error("❌ 场景设置失败，程序退出。")
        simulation_app.close()
        return

    print("\n🎉 仿真设置完成，开始实时计算循环...")

    try:
        while simulation_app.is_running():
            sim.step()
    except KeyboardInterrupt:
        print("\n用户手动中断仿真。")
    except Exception as e:
        carb.log_error(f"❌ 仿真循环中出现严重错误: {e}")
    finally:
        print("关闭仿真程序。")
        simulation_app.close()

if __name__ == "__main__":
    main()